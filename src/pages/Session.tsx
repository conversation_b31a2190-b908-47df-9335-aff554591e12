import {useEffect} from "react";
import {MainLayout} from "@/components/layout/MainLayout";
import {useQuery} from "@tanstack/react-query";
import {format} from "date-fns";
import {Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {Badge} from "@/components/ui/badge";
import {Button} from "@/components/ui/button";
import {Calendar, Clock, Download, FileAudio, FileText, Image} from "lucide-react";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {useToast} from "@/hooks/use-toast";
import {API_BASE_URL, authenticatedFetch} from "@/utils/authUtils";

// Define the session type
interface SessionConfig {
  // Configuration properties would go here
  [key: string]: any;
}

interface SessionText {
  // Text properties would go here
  [key: string]: any;
}

interface Session {
  session: string;
  audio: string;
  image: string;
  text: SessionText;
  pdf: string;
  config: SessionConfig;
  output: string | null;
  start: string;
  end: string | null;
  status: "In Progress" | "Success" | "Failed";
}

interface SessionResponse {
  body: Session[];
}

const fetchSessions = async (): Promise<Session[]> => {
  try {
    const response = await authenticatedFetch(
      `${API_BASE_URL}/sessions/list`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch sessions");
    }

    const data: SessionResponse = await response.json();

    // Sort sessions by start time in descending order (most recent first)
    const sortedSessions = data.body.sort((a, b) => {
      const dateA = new Date(a.start).getTime();
      const dateB = new Date(b.start).getTime();
      return dateB - dateA; // Descending order
    });

    return sortedSessions;
  } catch (error) {
    console.error("Error fetching sessions:", error);
    throw error;
  }
};

// Updated function to get download URL for input resources
const getInputResourceDownloadUrl = async (resourceName: string): Promise<string> => {
  if (!resourceName) return "";

  try {
    const response = await authenticatedFetch(
      `${API_BASE_URL}/input_resources/download?name=${encodeURIComponent(resourceName)}`
    );
    if (!response.ok) {
      throw new Error("Failed to get download URL");
    }

    const data = await response.json();
    return data.body || ""; // Extract URL from the body field
  } catch (error) {
    console.error("Error getting input resource download URL:", error);
    throw error;
  }
};

// Updated function to get download URL for output resources
const getOutputResourceDownloadUrl = async (resourceName: string): Promise<string> => {
  if (!resourceName) return "";

  try {
    const response = await authenticatedFetch(
      `${API_BASE_URL}/output_resources/download?name=${encodeURIComponent(resourceName)}`
    );

    if (!response.ok) {
      throw new Error("Failed to get download URL");
    }

    const data = await response.json();
    return data.body || ""; // Extract URL from body field
  } catch (error) {
    console.error("Error getting output resource download URL:", error);
    throw error;
  }
};

const SessionPage = () => {
  const { toast } = useToast();

  const { data: sessions, isLoading, error, refetch } = useQuery({
    queryKey: ['sessions'],
    queryFn: fetchSessions,
  });

  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Failed to load session history. Please try again.",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "In Progress":
        return <Badge className="bg-amber-500">In Progress</Badge>;
      case "Success":
        return <Badge className="bg-green-500">Success</Badge>;
      case "Failed":
        return <Badge className="bg-red-500">Failed</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Updated handleDownload function to use fetch to get file content as blob
  const handleDownload = async (resourceType: 'input' | 'output', resourceName: string | null) => {
    if (!resourceName) {
      toast({
        title: "Download Failed",
        description: "This file is not available for download.",
        variant: "destructive",
      });
      return;
    }

    try {
      toast({
        title: "Preparing Download",
        description: "Getting download URL...",
      });

      // Get the download URL based on resource type
      const downloadUrl = resourceType === 'input'
        ? await getInputResourceDownloadUrl(resourceName)
        : await getOutputResourceDownloadUrl(resourceName);

      if (!downloadUrl) {
        throw new Error("Failed to get download URL");
      }

      // Extract the original filename from the resource name
      const filename = resourceName.split('/').pop() || resourceName;

      // Instead of fetching the file content, directly use the presigned URL for download
      // This avoids CORS issues that occur when fetching from S3 presigned URLs
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename; // Default suggested filename
      a.target = '_blank'; // Open in new tab to handle any potential issues
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
      }, 1000);

      toast({
        title: "Download Started",
        description: `${filename} is downloading.`,
      });
    } catch (error) {
      console.error(`Error downloading ${resourceType} resource:`, error);
      toast({
        title: "Download Failed",
        description: `Failed to download file. Please try again.`,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    date.setHours(date.getHours() + 7);
    return format(date, "PPP");
  };

  const formatTime = (dateString: string | null) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    date.setHours(date.getHours() + 7);
    return format(date, "p");
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4">
        <Card className="shadow-md">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl font-bold">Session History</CardTitle>
              <Button onClick={() => refetch()} variant="outline" disabled={isLoading}>
                {isLoading ? "Loading..." : "Refresh"}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableCaption>List of your processing sessions</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Session ID</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Input Files</TableHead>
                      <TableHead>Output</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sessions && sessions.length > 0 ? (
                      sessions.map((session) => (
                        <TableRow key={session.session}>
                          <TableCell className="font-medium">{session.session.substring(0, 8)}...</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(session.start)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {formatTime(session.start)}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(session.status)}</TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              {session.image && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDownload('input', session.image)}
                                >
                                  <Image className="h-4 w-4 mr-1" />
                                  Image
                                </Button>
                              )}
                              {session.audio && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDownload('input', session.audio)}
                                >
                                  <FileAudio className="h-4 w-4 mr-1" />
                                  Audio
                                </Button>
                              )}
                              {session.pdf && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDownload('input', session.pdf)}
                                >
                                  <FileText className="h-4 w-4 mr-1" />
                                  PDF
                                </Button>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              variant={session.output ? "default" : "outline"}
                              disabled={!session.output}
                              onClick={() => handleDownload('output', session.output)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              {session.output ? "Download" : "Not Available"}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          {error ? "Error loading sessions" : "No sessions found"}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default SessionPage;
