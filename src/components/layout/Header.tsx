import {<PERSON><PERSON>} from "@/components/ui/button";
import {useNavigate} from "react-router-dom";
import {ThemeToggle} from "@/components/theme/ThemeToggle";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {Loader2, LogIn, LogOut, Settings, User} from "lucide-react";
import {useQuery} from "@tanstack/react-query";
import {Skeleton} from "@/components/ui/skeleton";
import {useAuth} from "@/contexts/AuthContext";
import {API_BASE_URL, authenticatedFetch} from "@/utils/authUtils";

// Define the user information interface
interface UserInformation {
  name: string;
  email: string;
  token: string;
  plan: string;
}

// Function to fetch user information
const fetchUserInformation = async (): Promise<UserInformation> => {
  const response = await authenticatedFetch(
    `${API_BASE_URL}/users/information`
  );
  if (!response.ok) {
    throw new Error("Failed to fetch user information");
  }

  const data = await response.json();

  // Check if the data is nested in a 'body' property (common in AWS API Gateway responses)
  if (data.body) {
    return data.body;
  }

  return data;
};

export const Header = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading, user, userAttributes, logout } = useAuth();

  // Fetch user information using React Query only if authenticated
  const { data: apiUser, isLoading: isApiLoading } = useQuery({
    queryKey: ['userInformation'],
    queryFn: fetchUserInformation,
    retry: 2,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: isAuthenticated, // Only run query if user is authenticated
  });
  // console.log(apiUser);
  // console.log(userAttributes)
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b shadow-sm">
      <div className="container mx-auto px-4">
        <nav className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            <h1 className="text-xl font-semibold bg-gradient-to-r from-[#1EAEDB] to-[#33C3F0] bg-clip-text text-transparent">MediaStudio</h1>
            <div className="hidden md:flex space-x-4">
              <Button
                variant="ghost"
                onClick={() => navigate("/home")}
                className="text-gray-700 hover:text-primary"
              >
                Home
              </Button>
              <Button
                variant="ghost"
                onClick={() => navigate("/studio")}
                className="text-gray-700 hover:text-primary"
              >
                Studio
              </Button>
              <Button
                variant="ghost"
                onClick={() => navigate("/session")}
                className="text-gray-700 hover:text-primary"
              >
                Sessions
              </Button>
              {/*<Button*/}
              {/*  variant="ghost"*/}
              {/*  onClick={() => navigate("/test-create-session")}*/}
              {/*  className="text-gray-700 hover:text-primary"*/}
              {/*>*/}
              {/*  Test Session*/}
              {/*</Button>*/}
              {/*<Button*/}
              {/*  variant="ghost"*/}
              {/*  onClick={() => navigate("/payment")}*/}
              {/*  className="text-gray-700 hover:text-primary"*/}
              {/*>*/}
              {/*  Payment*/}
              {/*</Button>*/}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {/*<ThemeToggle />*/}

            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full border border-blue-100 hover:bg-blue-50">
                    {isLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    ) : (
                      <Avatar className="h-9 w-9">
                        <AvatarImage src="" alt={userAttributes?.name || apiUser?.name || "User"} />
                        <AvatarFallback className="bg-gradient-to-r from-[#33C3F0] to-[#1EAEDB] text-white">
                          <User className="h-5 w-5" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-sm border border-blue-100">
                  <DropdownMenuLabel className="text-primary">My Account</DropdownMenuLabel>
                  <div className="px-2 py-1.5 text-sm">
                    {isLoading || isApiLoading ? (
                      <>
                        <Skeleton className="h-4 w-24 mb-1" />
                        <Skeleton className="h-3 w-32" />
                      </>
                    ) : (
                      <>
                        <div className="font-medium">{userAttributes?.name || apiUser?.name || "User"}</div>
                        <div className="text-xs text-muted-foreground">{userAttributes?.email || apiUser?.email || "No email available"}</div>
                      </>
                    )}
                  </div>
                  <DropdownMenuSeparator />
                  <div className="px-2 py-1.5 text-sm">
                    <div className="flex items-center justify-between">
                      <span>Available Tokens</span>
                      {isApiLoading ? (
                        <Skeleton className="h-4 w-8" />
                      ) : (
                        <span className="font-medium text-primary">{apiUser?.token || "0"}</span>
                      )}
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate("/profile")} className="hover:bg-blue-50">
                    <User className="mr-2 h-4 w-4 text-primary" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate("/settings")} className="hover:bg-blue-50">
                    <Settings className="mr-2 h-4 w-4 text-primary" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={async () => {
                      await logout();
                      navigate("/login");
                    }}
                    className="hover:bg-blue-50"
                  >
                    <LogOut className="mr-2 h-4 w-4 text-primary" />
                    <span>Sign out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                variant="outline"
                onClick={() => navigate("/login")}
                className="flex items-center gap-2"
              >
                <LogIn className="h-4 w-4" />
                Sign In
              </Button>
            )}
          </div>
        </nav>
      </div>
    </header>
  );
};
